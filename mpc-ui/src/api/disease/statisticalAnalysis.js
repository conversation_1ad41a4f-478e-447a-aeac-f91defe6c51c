import request from '@/utils/request'

let getCurrentSystem = () => {
    let currentSystemPath = localStorage.getItem('currentSysRouter') === 'disease' ? 'disease' : 'data'
    return currentSystemPath
}
/**
 *保存上传的数据
 *  excelName
 *  excelUrl
 *  type
 */
export function saveDataFile(params) {
    return request({
        url: `/${getCurrentSystem()}/statsdatafiles`,
        method: 'post',
        data: params
    })
}

/**
 *
 *获取数据列表
  pageNum: 1,
    pageSize: 10
 */
export function getMyFiles(params = {}) {
    return request({
        url: `/${getCurrentSystem()}/statsdatafiles/list`,
        method: 'get',
        params
    })
}


/**
 *
 * 删除某个数据
 * id
 */
export function delFileItem(id) {
    return request({
        url: `/${getCurrentSystem()}/statsdatafiles/${id}`,
        method: 'delete'
    })
}

/**
 * 获取文件的字段
 * sdStatisticsFilesId: 文件id
 */
export function getFileFields(params = {}) {
    return request({
        url: `/${getCurrentSystem()}/statsdatacolumn/list`,
        method: 'get',
        params
    })
}

/**
 * 获取描述性统计结果
 * resultType: 1,
 * resultName: 统计方式 + 选择的前三个字段
 * statisticsColumnList: 选择的字段列表
 * sdStatisticsFilesId: 文件id
 * columnNames: 选择的字段名称 逗号链接
 * openType: add 新增 edit 查看
 */
export function getDescriptiveStatisticsResult(query) {
    return request({
      url: '/disease/rStatistics/describe',
      method: 'post',
      data: query
    })
  }

/**
 * 获取分析历史记录
 * sdStatisticsFilesId: 文件id
 * pageNum
 * pageSize
 */
export function getStatsresult(params) {
    return request({
        url:`/${getCurrentSystem()}/statsresult/list`,
        method: 'get',
        params
    })
}

/**
 * 删除分析历史记录
 * id
 */
export function delRecord(id) {
    return request({
        url: `/${getCurrentSystem()}/statsresult/${id}`,
        method: 'delete'
    })
}

/**
 *单因素分析结果
 * resultType: 2,
 * resultName: 统计方式 + 选择的前三个字段
 * statisticsColumnList: 选择的字段列表
 * sdStatisticsFilesId: 文件id
 * columnNames: 选择的字段名称 逗号链接
 * openType: add 新增 edit 查看
 */

 export function getAov(params = {}) {
    return request({
        url: '/disease/rStatistics/singleAov',
        method: 'post',
        data: params
    })
 }

/**
 * 获取相关性分析结果
 * resultType: 3,
 * resultName: 统计方式 + 选择的前三个字段
 * statisticsColumnList: 选择的字段列表
 * sdStatisticsFilesId: 文件id
 * columnNames: 选择的字段名称 逗号链接
 * openType: add 新增 edit 查看
 * type: 分析类型  (pearson || spearman)
 */
export function getRcorr(params ={}) {
    return request({
        url: '/disease/rStatistics/rcorr',
        method: 'post',
        data: params
    })
}
/**
 * 获取多因素分析结果
 * resultType: 4,
 * resultName: 统计方式 + 选择的前三个字段
 * statisticsColumnList: 选择的字段列表
 * sdStatisticsFilesId: 文件id
 * columnNames: 选择的字段名称 逗号链接
 * openType: add 新增 edit 查看
 * isCompare： 是否进行事后多重比较 1是 0否
 * compareType:  分析类型 LSD, bonf , sidak
 */
export function getMultifactor(params = {}) {
    return request({
        url: '/disease/rStatistics/multiAov',
        method: 'post',
        data: params
    })
}


/**
 * 获取生存分析结果
 * resultType: 5,
 * resultName: 统计方式 + 选择的前三个字段
 * statisticsColumnList: 选择的字段列表
 * sdStatisticsFilesId: 文件id
 * columnNames: 选择的字段名称 逗号链接
 * openType: add 新增 edit 查看
 */
export function getSurvival(params = {}) {
    return request({
        url: '/disease/rStatistics/survival',
        method: 'post',
        data: params
    })
}

/**
 * 数据转换
 * columnId: 字段id
 * conversionType: 转换类型 1定类转定量 0定量转定类
 */
export function fieldConversion(params) {
    return request({
        url: `/${getCurrentSystem()}/statsdatacolumn/conversion`,
        method: 'post',
        data: params
    })
}

export function newFieldConversion(params) {
    return request({
        url: `/${getCurrentSystem()}/pythonDataProcessing/conversion`,
        method: 'post',
        data: params
    })
}

/**
 * 开始分析前的数据格式验证
 * 参数和统计接口参数一致
 */
export function checkExcel(params) {
    let url = ``
    if (getCurrentSystem() === 'disease') {
        url = '/disease/rStatistics/checkExcel'
    } else {
        url = '/data/statsdatafiles/checkExcel'
    }
    return request({
        url,
        method: 'post',
        data: params
    })
}

// 获取分析结果
export function getAnalysisResult(path,data) {
    return request({
        url: `/${getCurrentSystem()}/pStatistics/${path}`,
        method: 'post',
        data
    })
}

// 获取统计类型
export function statisticsType() {
    return request({
        url: `/${getCurrentSystem()}/pythonDataProcessing/statisticsType`,
        method: 'get'
    })
}

// 卡方检验校验
export function pearsonDataCheck(data) {
    return request({
        url: `/${getCurrentSystem()}/pStatistics/pearsonDataCheck`,
        method: 'post',
        data
    })
}

