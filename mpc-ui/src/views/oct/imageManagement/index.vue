<template>
  <div class="app-container">
    <MsCrud ref="crudRef" :columns="columns" :isShowAdd="false" :isSelection="true" api="oct/image/records/list" delApi="oct/image/records"
      :operateBtns="['view', 'del']" :operateWidth="150" @reset="onReset" @selectionChange="onSelectionChange">
      <!-- 自定义表头 -->
      <template #admitNo_headerSlot="{ row }">
        <div class="header-slot">
          <span>{{ row.label }}</span>
          <img v-if="batchAdmitNo" class="filter-icon" src="@/assets/images/filter-active.svg"
            @click="onAdmitNoFilter">
          <img v-else class="filter-icon" src="@/assets/images/filter.svg" @click="onAdmitNoFilter">
        </div>
      </template>
      <!-- 批量申请下载 -->
      <template #tableBtnAfter>
        <el-button size="small" @click="createTaskFn(null)">全部下载申请</el-button>
        <el-button size="small" :disabled="!selectionData || !selectionData.length" @click="createTaskFn(null)">批量下载申请</el-button>
        <!-- <el-button size="small" :disabled="!selectionData || !selectionData.length">批量删除</el-button> -->
      </template>
      <!-- 操作栏自定义 -->
      <template #operate="{ row }">
        <el-link type="primary" :underline="false" @click="createTaskFn(row)">下载申请</el-link>
      </template>
      <!-- 自定义详情 -->
      <template #drawerDetails="{ row }">
        <DetailSlot :deviceTypeList="deviceTypeList" :row="row" primaryKey="patientInfoId"/>
      </template>
    </MsCrud>
    <!-- 住院号弹窗 -->
    <el-dialog title="住院号批量筛选" :visible.sync="dialogVisible" width="600px">
      <el-input class="textarea" type="textarea" resize="none" v-model="batchAdmitNo"
        :placeholder="`每行输入一个编号, 按回车进行换行,例如:\n1111\n2222\n3333`"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import MsCrud from '@/components/MsCrud'
import DetailSlot from './detailSlot.vue'
import { manufacturerListAll, deviceListAll, createTask } from "@/api/oct/index";
export default {
  components: { MsCrud, DetailSlot },
  data() {
    return {
      columns: [
        {
          aliasName: "患者姓名",
          elementName: "patName",
          htmlType: "input",
          searchFlag: '1',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '1',
        },
        {
          aliasName: "住院号",
          elementName: "admitNo",
          htmlType: "input",
          searchFlag: '1',
          listFlag: '1',
          addFlag: '1',
          detailFlag: '1',
        },
        {
          aliasName: "设备类型",
          elementName: "deviceTypeId",
          htmlType: "select",
          options: [],
          searchFlag: '1',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "血管位置",
          elementName: "vesselLocation",
          htmlType: "select",
          dictType: "oct_vessel_location",
          searchFlag: '1',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "手术日期",
          elementName: "procedureDate",
          htmlType: "date",
          searchFlag: '0',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "手术开始日期",
          elementName: "startProcedureDate",
          htmlType: "date",
          searchFlag: '1',
          listFlag: '0',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "手术结束日期",
          elementName: "endProcedureDate",
          htmlType: "date",
          searchFlag: '1',
          listFlag: '0',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "手术类别",
          elementName: "procedureStage",
          htmlType: "select",
          dictType: "oct_procedure_stage",
          searchFlag: '1',
          listFlag: '1',
          addFlag: '1',
          detailFlag: '0',
        },
        {
          aliasName: "文件名称",
          elementName: "fileName",
          htmlType: "input",
          searchFlag: '0',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
        {
          aliasName: "品牌",
          elementName: "manufacturerInfoId",
          htmlType: "select",
          options: [],
          searchFlag: '1',
          listFlag: '1',
          addFlag: '0',
          detailFlag: '0',
        },
      ],
      dialogVisible: false,
      batchAdmitNo: '',
      selectionData: null
    }
  },
  async mounted() {
    await this.getAllManufacturer()
    await this.getDeviceTypeList()
    await this.$nextTick(() => {
      this.$refs.crudRef.reload()
    })
  },
  activated() {
    // 解决keep-alive缓存后表格样式错乱问题
    this.$refs.crudRef.$refs.tableRef.doLayout()
  },
  methods: {
    // 查询全部品牌
    getAllManufacturer() {
      manufacturerListAll().then(res => {
        this.columns.forEach(item => {
          if (item.elementName == 'manufacturerInfoId') {
            item.options = res.data.map(item => {
              return {
                label: item.manufacturerName,
                value: item.id
              }
            })
          }
        })
      })
    },
    // 获取设备类型
    getDeviceTypeList() {
      deviceListAll().then(res => {
        this.columns.forEach(item => {
          this.deviceTypeList = res.data
          if (item.elementName == 'deviceTypeId') {
            item.options = res.data.map(item => {
              return {
                label: item.typeName,
                value: item.id
              }
            })
          }
        })
      })
    },
    onReset() {
      this.batchAdmitNo = ''
    },
    onSelectionChange(val) {
      this.selectionData = val
    },
    // 住院号筛选
    onAdmitNoFilter() {
      this.batchAdmitNo = this.$refs.crudRef.formData.batchAdmitNo?.replace(/,/g, '\n')
      this.dialogVisible = true
    },
    onSubmit() {
      let batchAdmitNo = this.batchAdmitNo.replace(/\n/g, ',')
      this.$refs.crudRef.formData.batchAdmitNo = batchAdmitNo
      this.$refs.crudRef.onSearch()
      this.dialogVisible = false
    },
    createTaskFn(row) {
      let params = {
        taskName: '影像下载',
        processName: 'OCT影像下载申请',
        imageRecordsIds: row ? row.id : this.selectionData.map(item => item.id).join(',')
      }
      this.$prompt('自定义文件名称', '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        // params.customExportFileName = value
        createTask(params).then(res => {
          this.$message.success('申请成功')
        })
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.app-container {
  height: 100%;
  background: #f7f7f7;

  .header-slot {
    display: flex;
    align-items: center;

    .filter-icon {
      margin-left: 4px;
      width: 22px;
      cursor: pointer;
    }
  }

  .textarea {
    height: 30vh;

    ::v-deep textarea {
      height: 100% !important;
    }
  }
}
</style>